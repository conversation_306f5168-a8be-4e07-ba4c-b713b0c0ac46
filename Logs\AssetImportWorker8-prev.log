Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker8
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker8.log
-srvPort
51145
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21376] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 456211679 [EditorId] 456211679 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [21376] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 456211679 [EditorId] 456211679 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 115.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56868
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.014357 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 437 ms
Refreshing native plugins compatible for Editor in 97.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.647 seconds
Domain Reload Profiling:
	ReloadAssembly (1648ms)
		BeginReloadAssembly (190ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1274ms)
			LoadAssemblies (186ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (208ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (52ms)
			SetupLoadedEditorAssemblies (945ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (570ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (98ms)
				BeforeProcessingInitializeOnLoad (3ms)
				ProcessInitializeOnLoadAttributes (182ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.018743 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.555 seconds
Domain Reload Profiling:
	ReloadAssembly (2557ms)
		BeginReloadAssembly (260ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (2085ms)
			LoadAssemblies (197ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (117ms)
			SetupLoadedEditorAssemblies (1396ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (28ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1215ms)
				ProcessInitializeOnLoadMethodAttributes (40ms)
				AfterProcessingInitializeOnLoad (16ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2304 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (0.7 MB). Loaded Objects now: 2716.
Memory consumption went from 122.2 MB to 121.4 MB.
Total: 9.329300 ms (FindLiveObjects: 0.449000 ms CreateObjectMapping: 0.199300 ms MarkObjects: 7.642900 ms  DeleteObjects: 1.036200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3555.726168 seconds.
  path: Assets/3RDMODEASSET/TRACTOR TOCHAN/56.png
  artifactKey: Guid(3218a749be5c80a4390753d2de8805f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/TRACTOR TOCHAN/56.png using Guid(3218a749be5c80a4390753d2de8805f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e6d57fa27cd4bd3a49137b76e4e73d2d') in 0.219193 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.404282 seconds.
  path: Assets/Farming_/Population/textures/textures/accs_normal_001.dds
  artifactKey: Guid(cbca4567a077fed42b70cc91d5385a6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Farming_/Population/textures/textures/accs_normal_001.dds using Guid(cbca4567a077fed42b70cc91d5385a6e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c5f63e3cdd4e3413be1a419cd4ff0ede') in 0.148039 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/arrows/arrows-removebg-preview.png
  artifactKey: Guid(705341ad857bcb445907b6e5a645c972) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/arrows/arrows-removebg-preview.png using Guid(705341ad857bcb445907b6e5a645c972) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '618ea296402e7c5965fcaba6390ae860') in 0.098314 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Texture2D/arrow1.png
  artifactKey: Guid(7eee6b48eb6ce8c42a4d843c8a1bec43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Texture2D/arrow1.png using Guid(7eee6b48eb6ce8c42a4d843c8a1bec43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c504cca061ebb0cc294360bf70825e7f') in 0.111133 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.581565 seconds.
  path: Assets/Scenes/kachra/charcter/indian kurta.fbm/atlas.psd
  artifactKey: Guid(7a6e45a9de22bed4a931c34110f2fc57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/charcter/indian kurta.fbm/atlas.psd using Guid(7a6e45a9de22bed4a931c34110f2fc57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ddffd3b9443a74e008423d3e183ecad0') in 0.430021 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 3.681601 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha10.png
  artifactKey: Guid(8c9cfa1dbe00a1d41ae9d14f5ac543ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha10.png using Guid(8c9cfa1dbe00a1d41ae9d14f5ac543ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c9aca0c8397d9c991f480d4aa634e1fb') in 0.254696 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.713360 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha80.png
  artifactKey: Guid(595b166389c8db546a199430284ebc9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha80.png using Guid(595b166389c8db546a199430284ebc9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1d47907865e2799fb04819d7e7b7e919') in 0.117837 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 234.253543 seconds.
  path: Assets/pngwing.com (1).png
  artifactKey: Guid(05eebe1f9053ee44c83fcae0e5121b26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/pngwing.com (1).png using Guid(05eebe1f9053ee44c83fcae0e5121b26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd8a34cf7c44e076cbcb99007ed4555ad') in 0.146389 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 1.867096 seconds.
  path: Assets/Spark.mat
  artifactKey: Guid(1aa2b705e2ef111459c359c6ffb5b6cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Spark.mat using Guid(1aa2b705e2ef111459c359c6ffb5b6cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd3c25dfeb84c0b3afab6dcdf2d02b8e9') in 0.191552 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2547.914322 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/Circle2.mat
  artifactKey: Guid(8fce5540653d6d8439e52364661e10c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/Circle2.mat using Guid(8fce5540653d6d8439e52364661e10c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '832442da721f3fd9236f46957e0c5f19') in 0.044348 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 31.263382 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/AoE effects
  artifactKey: Guid(f0e78b2e61a2b2f40bf0a0be9e842772) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/AoE effects using Guid(f0e78b2e61a2b2f40bf0a0be9e842772) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a63a8042321b53f58b38c8e2a8d56544') in 0.014172 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 6.570290 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/Circle.mat
  artifactKey: Guid(4bab0f95134663b4c9c3dd73939c0ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/Circle.mat using Guid(4bab0f95134663b4c9c3dd73939c0ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '33c66d7f420c31ada9b828616acb90d7') in 0.025394 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 19.302499 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/MagicCircle2.mat
  artifactKey: Guid(b56ee68f36d76a648a742b6874f4d4a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/MagicCircle2.mat using Guid(b56ee68f36d76a648a742b6874f4d4a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3fc7d29c70554f2a969e0403004ee4e3') in 0.044614 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 27.157255 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/MagicCircle.mat
  artifactKey: Guid(ddac23d301cbfca45bcbe217c05f0141) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/MagicCircle.mat using Guid(ddac23d301cbfca45bcbe217c05f0141) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '407381d483b6153f8dc61e1c6ff05681') in 0.066150 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 183.330517 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/Point.mat
  artifactKey: Guid(e546d2d7171981b468cf29388d902e99) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/Point.mat using Guid(e546d2d7171981b468cf29388d902e99) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3425690abca4da58f5096db0c3d62ea7') in 0.031109 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 96.616486 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3bb15b1a47c1e8ceee41e196a234ab93') in 0.121348 seconds 
Number of asset objects unloaded after import = 49
========================================================================
Received Import Request.
  Time since last request: 125.116875 seconds.
  path: Assets/assets/Particlecollection_Free samples/Material/dark_02.mat
  artifactKey: Guid(deff24b2020c3cd4981d5d891e4ce3a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/Particlecollection_Free samples/Material/dark_02.mat using Guid(deff24b2020c3cd4981d5d891e4ce3a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dbc941c5c205b6e5b6a87fe24abfeea3') in 0.029004 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 41.727148 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab
  artifactKey: Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab using Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bdf85ab5c41791748fd6a37b97b4e773') in 0.120189 seconds 
Number of asset objects unloaded after import = 89
========================================================================
Received Import Request.
  Time since last request: 29.011957 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab
  artifactKey: Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab using Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '520fba94b23b0c290888a8aee4b569a1') in 0.086778 seconds 
Number of asset objects unloaded after import = 89
========================================================================
Received Import Request.
  Time since last request: 23.655999 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab
  artifactKey: Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab using Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '110cca1cfdc9f3c5d57382ebbfd8d789') in 0.088218 seconds 
Number of asset objects unloaded after import = 89
========================================================================
Received Import Request.
  Time since last request: 9.003628 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab
  artifactKey: Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point (1) Variant.prefab using Guid(ed223655a3e3c8f4e8d573d03f114ea2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd2d2e2c02112a5c38c9b98737e6c04f6') in 0.077950 seconds 
Number of asset objects unloaded after import = 89
========================================================================
Received Import Request.
  Time since last request: 38.960518 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '45cc27dad8c0db3b06fa08a87aba7cb1') in 0.105902 seconds 
Number of asset objects unloaded after import = 49
========================================================================
Received Import Request.
  Time since last request: 215.214519 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f73e137d7222756734e3997ea880b2f5') in 0.153707 seconds 
Number of asset objects unloaded after import = 62
========================================================================
Received Import Request.
  Time since last request: 1727.630371 seconds.
  path: Assets/GameMy/Water_Fountain/Materials/Fountain_Jet_B_mtl.mat
  artifactKey: Guid(0d180e3ca4e2b9640a8bc7ebee54e024) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/Water_Fountain/Materials/Fountain_Jet_B_mtl.mat using Guid(0d180e3ca4e2b9640a8bc7ebee54e024) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b4629a67787e6480c8f102c2c17c1dee') in 0.044697 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2.845567 seconds.
  path: Assets/GameMy/Water_Fountain/Materials/Splash_mtl.mat
  artifactKey: Guid(3d8404aae7561ec469923e408073e343) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/Water_Fountain/Materials/Splash_mtl.mat using Guid(3d8404aae7561ec469923e408073e343) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8c6714bd572e3b9e61451ec98b1fe7d9') in 0.022171 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 23.257591 seconds.
  path: Assets/GameMy/Water_Fountain/Textures/Fountain_Jet_TextureSheet.tif
  artifactKey: Guid(48c3e9d37fa514748bcb2d72ca226aac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/Water_Fountain/Textures/Fountain_Jet_TextureSheet.tif using Guid(48c3e9d37fa514748bcb2d72ca226aac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1397afc336f2b50a8f372556e70f1ba5') in 0.026804 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 1511.048201 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/Heart.mat
  artifactKey: Guid(e97a79db1dd66ad4d8f70932abe94b73) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/Heart.mat using Guid(e97a79db1dd66ad4d8f70932abe94b73) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7df775a3bd258375f58ccd1003919c46') in 0.054308 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 84.220303 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Materials/Star.mat
  artifactKey: Guid(f7fb42690dbaf0d408f1f55ea89b7be8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Materials/Star.mat using Guid(f7fb42690dbaf0d408f1f55ea89b7be8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b18ce5aff4ddb955b033f62352950c29') in 0.028161 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 84.676298 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Comp.prefab
  artifactKey: Guid(caca75cd3d6f1154aae1ee3679e0335e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Comp.prefab using Guid(caca75cd3d6f1154aae1ee3679e0335e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b2c7d06e0afcee468b18b9bbd4d3bc88') in 0.070771 seconds 
Number of asset objects unloaded after import = 117
========================================================================
Received Import Request.
  Time since last request: 40.608254 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Comp.prefab
  artifactKey: Guid(caca75cd3d6f1154aae1ee3679e0335e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Comp.prefab using Guid(caca75cd3d6f1154aae1ee3679e0335e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8514924fc68dd57ececdfe231cecf400') in 0.039595 seconds 
Number of asset objects unloaded after import = 61
========================================================================
Received Import Request.
  Time since last request: 246.832997 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Complete Point.prefab
  artifactKey: Guid(7d2e1e3aee5103a49a0da21fff5e03cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Complete Point.prefab using Guid(7d2e1e3aee5103a49a0da21fff5e03cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '658325f9fc7f2313b559927062446be8') in 0.233329 seconds 
Number of asset objects unloaded after import = 66
========================================================================
Received Import Request.
  Time since last request: 759.302521 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/COMPLETE/LEVEL COMPLETED copy.png
  artifactKey: Guid(728347debc81909478eebb56570df7ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/COMPLETE/LEVEL COMPLETED copy.png using Guid(728347debc81909478eebb56570df7ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd86053490ec5dbab8954610db79d14d0') in 0.079215 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.031671 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/LOADING TEXT_.png
  artifactKey: Guid(54850aa64c195bd4f9e1d28f708a1dce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/LOADING TEXT_.png using Guid(54850aa64c195bd4f9e1d28f708a1dce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e0ef1d16c5e1c1d62ba2a73a061918f') in 0.093940 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/LOGO_.png
  artifactKey: Guid(b43137a293952e848b083bbc0a6d3623) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/LOGO_.png using Guid(b43137a293952e848b083bbc0a6d3623) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b42d4aa36d1b2a43d6fbb001f721f245') in 0.292132 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/tractor ui/changis/loading/LOGO_.png
  artifactKey: Guid(29bb3a0754e273940b4c6c8305fa77d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/loading/LOGO_.png using Guid(29bb3a0754e273940b4c6c8305fa77d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '21958e5a9e86d4173623b359f1b423ec') in 0.665083 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/LEVEL NO.png
  artifactKey: Guid(222ba20b1f5fce84da59a61c1ced90cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/LEVEL NO.png using Guid(222ba20b1f5fce84da59a61c1ced90cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7d3c469da09a8765e19d90e34b72eda3') in 0.226020 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/PAUSE/LEVEL PAUSE.png
  artifactKey: Guid(90c77d8d5a9ceed47a191535f815f84c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/PAUSE/LEVEL PAUSE.png using Guid(90c77d8d5a9ceed47a191535f815f84c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '50b0e7c135933d8c35082fa83df6e3eb') in 0.099847 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.325409 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MODES/main menu  copy (1).png
  artifactKey: Guid(4f16fe92012a6ad48a7155a0c7405e0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MODES/main menu  copy (1).png using Guid(4f16fe92012a6ad48a7155a0c7405e0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b7acc2eca61718f0e850492689d8fb32') in 0.106730 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MODES/MODES.png
  artifactKey: Guid(aa8b0f558f143db4ca8b34c0a39e952f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MODES/MODES.png using Guid(aa8b0f558f143db4ca8b34c0a39e952f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '04f2690c64c2d8768fbdae4c9f9a54d3') in 0.081445 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Dashboard/NeedleTop.png
  artifactKey: Guid(628cc69884b489440889479d274302c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Dashboard/NeedleTop.png using Guid(628cc69884b489440889479d274302c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f33f43bf25270988446ba44a156a0e41') in 0.030585 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/COMPLETE/NEXT_.png
  artifactKey: Guid(a8a3ec62c17185642b6a9ef18d177e15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/COMPLETE/NEXT_.png using Guid(a8a3ec62c17185642b6a9ef18d177e15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '46f69ccde59fb123581cbcbacdc1a9b9') in 0.082707 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Dashboard/NoS.png
  artifactKey: Guid(b0c6a6d53beedd541a008c1870b9fe4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Dashboard/NoS.png using Guid(b0c6a6d53beedd541a008c1870b9fe4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '01232ffd59c09a801169099295487109') in 0.043641 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/MUSIC ON_.png
  artifactKey: Guid(8cde7a32333ec474e9aa551ab129d633) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/MUSIC ON_.png using Guid(8cde7a32333ec474e9aa551ab129d633) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2a62070ea543e314fc61419db64ca8e5') in 0.035715 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/PAUSE.png
  artifactKey: Guid(7682e1f90c0ec5847946f8bdebe8cca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/PAUSE.png using Guid(7682e1f90c0ec5847946f8bdebe8cca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cb655fcb12ebafba6eb98d9fd29f8dec') in 0.030844 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Scenes/kachra/SLICING 2/EXIT/NO_.png
  artifactKey: Guid(7b01f60d3e3890743922711d958bb4e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/EXIT/NO_.png using Guid(7b01f60d3e3890743922711d958bb4e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0331266b4764583b6e6178eaa800a5eb') in 0.159064 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/MORE GAMES_.png
  artifactKey: Guid(8ee1993325392c1418584456f13ee168) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/MORE GAMES_.png using Guid(8ee1993325392c1418584456f13ee168) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b8149187531c79bc0961734a9d03c44a') in 0.104336 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/3RDMODEASSET/Pakistan-flag-png-high-quality-.png
  artifactKey: Guid(5978f8a7981ecab439ee52159b6b3ba3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/Pakistan-flag-png-high-quality-.png using Guid(5978f8a7981ecab439ee52159b6b3ba3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5049cca5574faac9d0e9da120375c6f6') in 0.147991 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/EXIT/NO_.png
  artifactKey: Guid(8c0ce43cff9594f48a4d6cce3f77f4fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/EXIT/NO_.png using Guid(8c0ce43cff9594f48a4d6cce3f77f4fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'af73a60c4db44e891cac9d7841d69e70') in 0.083163 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/INSTRUCTION PANAL/OK.png
  artifactKey: Guid(94f0ec5555e63b44dab509b6d0b5599d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/INSTRUCTION PANAL/OK.png using Guid(94f0ec5555e63b44dab509b6d0b5599d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e20f3d13ca8e65c4b1dd7470a22e86e5') in 0.061483 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/tractor ui/changis/PRIVACY POLICY.png
  artifactKey: Guid(f44b8099279e999438dd35cf13e77219) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/PRIVACY POLICY.png using Guid(f44b8099279e999438dd35cf13e77219) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b6afdd23f64cbb1c0acb245f26f48cfc') in 0.136419 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Scenes/kachra/WEATHER/rain off.png
  artifactKey: Guid(053d4094c47dc8345989db7631e90431) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/WEATHER/rain off.png using Guid(053d4094c47dc8345989db7631e90431) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'daf732f4e0b318376d1c94484347d531') in 0.043328 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Scenes/kachra/SLICING 2/profile/Rectangle 4.png
  artifactKey: Guid(c0d08e15057735a4caa552ca98920ffd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/profile/Rectangle 4.png using Guid(c0d08e15057735a4caa552ca98920ffd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '437318b61b2efd2426d4e4f36f4a95bf') in 0.105487 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/Rectangle 1114.png
  artifactKey: Guid(5e1c748b50d6f8e499c4c899ff9004d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/Rectangle 1114.png using Guid(5e1c748b50d6f8e499c4c899ff9004d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ad66dfa8e9b6f742940c35c5ae3aae3') in 0.085834 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/PRIVACY_.png
  artifactKey: Guid(7834b547889e1e24aa5da17883797ae7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/PRIVACY_.png using Guid(7834b547889e1e24aa5da17883797ae7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b268e758949306f27601d44853d0131') in 0.145406 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Dashboard/Park.png
  artifactKey: Guid(8e86ff30d1da80b44a95d7264bc409f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Dashboard/Park.png using Guid(8e86ff30d1da80b44a95d7264bc409f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c46fcc2827857cef5634484a02c8a135') in 0.029010 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/tractor ui/changis/race_.png
  artifactKey: Guid(4233bc1f479c7414c91d5ebd77be5114) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/race_.png using Guid(4233bc1f479c7414c91d5ebd77be5114) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a19dcb51e08eec9de09d86b1254599be') in 0.069800 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/tractor ui/changis/PRIVACY (2).png
  artifactKey: Guid(ca45a6cc9daeb6a449cef35d18dd7884) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/PRIVACY (2).png using Guid(ca45a6cc9daeb6a449cef35d18dd7884) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd56df0151592d3ddd717fe28c3ce45d0') in 0.157267 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/RE TRAY_.png
  artifactKey: Guid(cf1bb5900c0574a42b2e415d9afec226) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/RE TRAY_.png using Guid(cf1bb5900c0574a42b2e415d9afec226) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1ae71916ca8e4e3fd8df1fa0f90fe10a') in 0.032305 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/OPEN DOOR.png
  artifactKey: Guid(cd61518cd79f4634d8d330edd9d7361e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/OPEN DOOR.png using Guid(cd61518cd79f4634d8d330edd9d7361e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '72031faab1bac2a0208117d308711560') in 0.083756 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/HousePack/Models/House1/Textures/map9.bmp
  artifactKey: Guid(06d76b9db2c6726488b984bad5548abd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/HousePack/Models/House1/Textures/map9.bmp using Guid(06d76b9db2c6726488b984bad5548abd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ea8801ae3d2441076b65ae2514b43bcc') in 0.088302 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/RATE US_.png
  artifactKey: Guid(0ba201cec5c629a4bb005c3f8fc6d064) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/RATE US_.png using Guid(0ba201cec5c629a4bb005c3f8fc6d064) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e256a38e2291a3123e67b3c9bba029de') in 0.154900 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/texture/pngtree-wrought-iron-railing-fence-png-image_3140975-removebg-preview.png
  artifactKey: Guid(55df64931f469c2469476de9bbc8de75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/pngtree-wrought-iron-railing-fence-png-image_3140975-removebg-preview.png using Guid(55df64931f469c2469476de9bbc8de75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f20d0efc7410a314212fb265e3f032e3') in 0.100415 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Scenes/kachra/SLICING 2/MODE/play copy.png
  artifactKey: Guid(6f7f96053da74c242abc1966a6fa9fbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/MODE/play copy.png using Guid(6f7f96053da74c242abc1966a6fa9fbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f3668821d59a41f30dd8cd10c1101299') in 0.113204 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/3RDMODEASSET/nepal-Flag-Png-Free-.png
  artifactKey: Guid(d4b6340e9742f2a439cf08c0dcd41339) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/nepal-Flag-Png-Free-.png using Guid(d4b6340e9742f2a439cf08c0dcd41339) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c4ee3418725b2c916273bcc3023b7b45') in 0.193343 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 58.897906 seconds.
  path: Assets/City/EasyRoads3D scenes/Terrain Assets/Terrain Textures/Cliff (Layered Rock) ER.psd
  artifactKey: Guid(18214e9d6af6248559d501391856f1c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/City/EasyRoads3D scenes/Terrain Assets/Terrain Textures/Cliff (Layered Rock) ER.psd using Guid(18214e9d6af6248559d501391856f1c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '791d3512b19661e0b5ae93cb928a7374') in 0.167629 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_play.png
  artifactKey: Guid(2a563a43c33f0f3428d02b3a43886fc2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_play.png using Guid(2a563a43c33f0f3428d02b3a43886fc2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '38f24aaed9a9741a3cad3737a53dcf64') in 0.085057 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.298124 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 12.png
  artifactKey: Guid(1158cc555e447b6409959c3633081e16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 12.png using Guid(1158cc555e447b6409959c3633081e16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5a793340733d11d6f73b70c4b6dc0e59') in 0.100495 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/3RDMODEASSET/TRACTOR TOCHAN/Layer 17 copy.png
  artifactKey: Guid(2c77868884dbb6c4ea9b4f76770499da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/TRACTOR TOCHAN/Layer 17 copy.png using Guid(2c77868884dbb6c4ea9b4f76770499da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9bf8b5ab0a4eab40433ab4edcdb0819f') in 0.081362 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/3RDMODEASSET/TRACTOR TOCHAN/Materials/Layer 17 copy.mat
  artifactKey: Guid(a9abe8854beb0e4418d99d42bea9c2c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/TRACTOR TOCHAN/Materials/Layer 17 copy.mat using Guid(a9abe8854beb0e4418d99d42bea9c2c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e773e3f9cf16c604f2ce71f7a35855cb') in 0.140971 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 227.245516 seconds.
  path: Assets/arrows/direction arrow.png
  artifactKey: Guid(a74d8d498bf9c464bacf16f942fad98a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/arrows/direction arrow.png using Guid(a74d8d498bf9c464bacf16f942fad98a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cffd4672db69b86456ca6747ca78fe4f') in 0.137988 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 12.421703 seconds.
  path: Assets/arrows/arrows-removebg-preview.png
  artifactKey: Guid(705341ad857bcb445907b6e5a645c972) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/arrows/arrows-removebg-preview.png using Guid(705341ad857bcb445907b6e5a645c972) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b7b4d5d820afa8ca79aa9b1b5764fe65') in 0.044651 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 335.106760 seconds.
  path: Assets/Sounds/eklee-KeyPressMac07.wav
  artifactKey: Guid(8f0b3287481617d448b4857ba5350d10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Sounds/eklee-KeyPressMac07.wav using Guid(8f0b3287481617d448b4857ba5350d10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4d82f0c1f1bcbbaf8d92e1558831b054') in 0.095284 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016536 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.380 seconds
Domain Reload Profiling:
	ReloadAssembly (2381ms)
		BeginReloadAssembly (372ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (15ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (130ms)
		EndReloadAssembly (1847ms)
			LoadAssemblies (178ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (340ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1231ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1087ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 5.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 2843.
Memory consumption went from 124.4 MB to 123.7 MB.
Total: 8.638600 ms (FindLiveObjects: 0.351300 ms CreateObjectMapping: 0.173700 ms MarkObjects: 7.290800 ms  DeleteObjects: 0.821300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 724.835371 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '63252871fb186d2b9be29ec79008ffef') in 0.042696 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014191 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.174 seconds
Domain Reload Profiling:
	ReloadAssembly (2175ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1773ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1213ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1068ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2858.
Memory consumption went from 124.4 MB to 123.7 MB.
Total: 8.073400 ms (FindLiveObjects: 0.435400 ms CreateObjectMapping: 0.162100 ms MarkObjects: 6.350700 ms  DeleteObjects: 1.122600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014136 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.169 seconds
Domain Reload Profiling:
	ReloadAssembly (2170ms)
		BeginReloadAssembly (244ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (1773ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (310ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1214ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1072ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2873.
Memory consumption went from 124.4 MB to 123.7 MB.
Total: 10.350300 ms (FindLiveObjects: 0.431300 ms CreateObjectMapping: 0.206800 ms MarkObjects: 7.912000 ms  DeleteObjects: 1.797400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 350.862391 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '677652db47c23057f2e4091543d0f999') in 0.183037 seconds 
Number of asset objects unloaded after import = 82
========================================================================
Received Import Request.
  Time since last request: 9.699988 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Hits and explosions/Holy hit.prefab
  artifactKey: Guid(e190bf97c041e58448af6b043116cba0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Hits and explosions/Holy hit.prefab using Guid(e190bf97c041e58448af6b043116cba0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'daac3cc2e078b2e95e7d361c7e8378fe') in 0.042470 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 8.538844 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '256bc0c3c5b2cc276a761d93164f79c4') in 0.074391 seconds 
Number of asset objects unloaded after import = 82
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014467 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.197 seconds
Domain Reload Profiling:
	ReloadAssembly (2198ms)
		BeginReloadAssembly (249ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (1791ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (312ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (67ms)
			SetupLoadedEditorAssemblies (1222ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1082ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 156 unused Assets / (0.7 MB). Loaded Objects now: 2888.
Memory consumption went from 124.4 MB to 123.7 MB.
Total: 6.891100 ms (FindLiveObjects: 0.411400 ms CreateObjectMapping: 0.176800 ms MarkObjects: 5.463400 ms  DeleteObjects: 0.838100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 133.027930 seconds.
  path: Assets/RealisticCarControllerV3/Models/Environment/Broadleaf_Mobile Materials/LOD1/Branches_0.mat
  artifactKey: Guid(23b2e65059f9e0d44b1eff4dcdc0b993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Models/Environment/Broadleaf_Mobile Materials/LOD1/Branches_0.mat using Guid(23b2e65059f9e0d44b1eff4dcdc0b993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4c2a1c72d3d7361a5c168d0341a00805') in 0.139879 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Blank.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Blank.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7caa4420cab37739c40c55b91bbc0cc5') in 0.030131 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Shesha/Chelam.mat
  artifactKey: Guid(73302520e3ee61843a0a60052215e47e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Shesha/Chelam.mat using Guid(73302520e3ee61843a0a60052215e47e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f5c597c99bc11461a0ba70aefaf666ef') in 0.024580 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Checked.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Checked.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5bdcdc3eb0306c3bf6d921dfa5a1179b') in 0.021986 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Texture2D/checkereds.png
  artifactKey: Guid(68075aa3cf3abe147abd00fa4d2400ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Texture2D/checkereds.png using Guid(68075aa3cf3abe147abd00fa4d2400ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ff79df0256f38e81e5c71e01483c2ecd') in 0.021905 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Blank-Pressed.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Blank-Pressed.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9457519592c41dfd3580cbf1185d050d') in 0.028247 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Checked-Pressed.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/BuildReport/GUI/NativeSkin/Dark/Checkbox-Checked-Pressed.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8409e92eb17c967e027aa0cd07ee2961') in 0.019136 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.236464 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/tileCheckerboard.png
  artifactKey: Guid(1954196ce2bc5c3478b215b39212c3cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/tileCheckerboard.png using Guid(1954196ce2bc5c3478b215b39212c3cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fccd8f12349ac98591d875055fe8584f') in 0.021506 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 12.914034 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Hits and explosions/Electro hit.prefab
  artifactKey: Guid(a164e676dbbd6e54587237e619372c98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Hits and explosions/Electro hit.prefab using Guid(a164e676dbbd6e54587237e619372c98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7d37407d29c4b9e03bd565889fb6051') in 0.060062 seconds 
Number of asset objects unloaded after import = 18
========================================================================
Received Import Request.
  Time since last request: 1.888340 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Hits and explosions/Snow hit.prefab
  artifactKey: Guid(0edef91621bb6b34384ccc9dbb98729c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Hits and explosions/Snow hit.prefab using Guid(0edef91621bb6b34384ccc9dbb98729c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3e43100165a5b771518b6ae1e9dd048a') in 0.085858 seconds 
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/SETTING/STERING WHITE.png
  artifactKey: Guid(c9a5e1dc05aaa714b83ea4a14d2baef2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/SETTING/STERING WHITE.png using Guid(c9a5e1dc05aaa714b83ea4a14d2baef2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '92320fab6024e96b316bd3af5beea928') in 0.040112 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 2.617182 seconds.
  path: Assets/Sounds/FruitSellers Sound.mp3
  artifactKey: Guid(f8bf16b99c4bed041a6db838983e32b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Sounds/FruitSellers Sound.mp3 using Guid(f8bf16b99c4bed041a6db838983e32b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7144eeb201f9582c090b41461e264860') in 0.042904 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/3RDMODEASSET/TRACTOR TOCHAN/TRACTOR TOCHAN.fbm/Tractor_Sound_System.png
  artifactKey: Guid(c5c729efaf0bc664fb3edcb2bb8f2f59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/TRACTOR TOCHAN/TRACTOR TOCHAN.fbm/Tractor_Sound_System.png using Guid(c5c729efaf0bc664fb3edcb2bb8f2f59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ea751e54a30b2b9003341346abd8ee61') in 0.074689 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 13.463991 seconds.
  path: Assets/plsyerasset/farmer Character/Tractor_Sound_System.png
  artifactKey: Guid(9f1e1cb6c72357c499b34452b3832b54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/plsyerasset/farmer Character/Tractor_Sound_System.png using Guid(9f1e1cb6c72357c499b34452b3832b54) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '32362e937ce2e38567b25ac08d036b45') in 0.028474 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2.268948 seconds.
  path: Assets/Sounds/Water Sound Effect.mp3
  artifactKey: Guid(79f75f8823a24b54d9455efca57f3bbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Sounds/Water Sound Effect.mp3 using Guid(79f75f8823a24b54d9455efca57f3bbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '438e554c7930202c69418cb68dae4093') in 0.027634 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015668 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.170 seconds
Domain Reload Profiling:
	ReloadAssembly (2171ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (1769ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (320ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1191ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1049ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2908.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 7.282200 ms (FindLiveObjects: 0.531600 ms CreateObjectMapping: 0.211300 ms MarkObjects: 5.688200 ms  DeleteObjects: 0.849700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015849 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.42 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.183 seconds
Domain Reload Profiling:
	ReloadAssembly (2184ms)
		BeginReloadAssembly (231ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1791ms)
			LoadAssemblies (153ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1228ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1082ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2923.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 8.510000 ms (FindLiveObjects: 0.374300 ms CreateObjectMapping: 0.172100 ms MarkObjects: 7.116400 ms  DeleteObjects: 0.845900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015797 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.175 seconds
Domain Reload Profiling:
	ReloadAssembly (2176ms)
		BeginReloadAssembly (239ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1783ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1223ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1081ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2938.
Memory consumption went from 124.6 MB to 123.8 MB.
Total: 7.317800 ms (FindLiveObjects: 0.425000 ms CreateObjectMapping: 0.180100 ms MarkObjects: 5.913500 ms  DeleteObjects: 0.798100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015966 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.173 seconds
Domain Reload Profiling:
	ReloadAssembly (2174ms)
		BeginReloadAssembly (237ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1777ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (72ms)
			SetupLoadedEditorAssemblies (1223ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1082ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2953.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 6.851100 ms (FindLiveObjects: 0.354200 ms CreateObjectMapping: 0.164700 ms MarkObjects: 5.443100 ms  DeleteObjects: 0.887400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015883 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.182 seconds
Domain Reload Profiling:
	ReloadAssembly (2183ms)
		BeginReloadAssembly (229ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (50ms)
		EndReloadAssembly (1788ms)
			LoadAssemblies (150ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (319ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1218ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1070ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2968.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.306700 ms (FindLiveObjects: 0.359800 ms CreateObjectMapping: 0.163900 ms MarkObjects: 5.767500 ms  DeleteObjects: 1.014200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016028 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.183 seconds
Domain Reload Profiling:
	ReloadAssembly (2184ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (1781ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (312ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1210ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1064ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2983.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.974800 ms (FindLiveObjects: 0.640500 ms CreateObjectMapping: 0.170600 ms MarkObjects: 6.062700 ms  DeleteObjects: 1.099000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016615 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.228 seconds
Domain Reload Profiling:
	ReloadAssembly (2229ms)
		BeginReloadAssembly (244ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (1823ms)
			LoadAssemblies (156ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (318ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (72ms)
			SetupLoadedEditorAssemblies (1255ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1112ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2998.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 10.945300 ms (FindLiveObjects: 0.979200 ms CreateObjectMapping: 0.229900 ms MarkObjects: 8.501600 ms  DeleteObjects: 1.230900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015950 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.59 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.181 seconds
Domain Reload Profiling:
	ReloadAssembly (2182ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1775ms)
			LoadAssemblies (168ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (316ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (72ms)
			SetupLoadedEditorAssemblies (1209ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1064ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3013.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.027600 ms (FindLiveObjects: 0.372100 ms CreateObjectMapping: 0.160300 ms MarkObjects: 5.745500 ms  DeleteObjects: 0.748500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016329 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.173 seconds
Domain Reload Profiling:
	ReloadAssembly (2174ms)
		BeginReloadAssembly (232ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1783ms)
			LoadAssemblies (153ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (319ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1214ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1073ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3028.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 7.325600 ms (FindLiveObjects: 0.401200 ms CreateObjectMapping: 0.165100 ms MarkObjects: 5.917800 ms  DeleteObjects: 0.839900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015811 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.202 seconds
Domain Reload Profiling:
	ReloadAssembly (2204ms)
		BeginReloadAssembly (236ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1812ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (320ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1228ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1086ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3043.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 7.893700 ms (FindLiveObjects: 0.377700 ms CreateObjectMapping: 0.173900 ms MarkObjects: 6.163300 ms  DeleteObjects: 1.177700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015528 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.180 seconds
Domain Reload Profiling:
	ReloadAssembly (2181ms)
		BeginReloadAssembly (244ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1775ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (314ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (69ms)
			SetupLoadedEditorAssemblies (1213ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1069ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3058.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 6.846300 ms (FindLiveObjects: 0.493800 ms CreateObjectMapping: 0.167500 ms MarkObjects: 5.400400 ms  DeleteObjects: 0.783600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016161 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.238 seconds
Domain Reload Profiling:
	ReloadAssembly (2240ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (11ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (1783ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (311ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1195ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (1051ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3073.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 14.830600 ms (FindLiveObjects: 0.562900 ms CreateObjectMapping: 0.289600 ms MarkObjects: 12.500200 ms  DeleteObjects: 1.475600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015910 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.182 seconds
Domain Reload Profiling:
	ReloadAssembly (2183ms)
		BeginReloadAssembly (260ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1748ms)
			LoadAssemblies (173ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1186ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1045ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3088.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 6.933100 ms (FindLiveObjects: 0.356200 ms CreateObjectMapping: 0.161300 ms MarkObjects: 5.683300 ms  DeleteObjects: 0.731000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016374 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.203 seconds
Domain Reload Profiling:
	ReloadAssembly (2204ms)
		BeginReloadAssembly (256ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (1787ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (315ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1221ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1079ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3103.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 7.669600 ms (FindLiveObjects: 0.586800 ms CreateObjectMapping: 0.330400 ms MarkObjects: 5.928000 ms  DeleteObjects: 0.822800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014349 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.236 seconds
Domain Reload Profiling:
	ReloadAssembly (2237ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1837ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (305ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1279ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1139ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3118.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 8.258200 ms (FindLiveObjects: 0.587700 ms CreateObjectMapping: 0.175000 ms MarkObjects: 6.626200 ms  DeleteObjects: 0.866900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 10936.695900 seconds.
  path: Assets/HousePack/Models/House8/Materials/Bow_Abattoir_Conc2.mat
  artifactKey: Guid(4f4e6861e2cd3bb4ca5ec48a5d0be280) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/HousePack/Models/House8/Materials/Bow_Abattoir_Conc2.mat using Guid(4f4e6861e2cd3bb4ca5ec48a5d0be280) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd59279cd1cb86981cd3b4c3d8ad7f7b3') in 0.168240 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Newasset/Materials/Concrete3Diffuse.mat
  artifactKey: Guid(af01ac3020580f0478c63a3c1afa798e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Newasset/Materials/Concrete3Diffuse.mat using Guid(af01ac3020580f0478c63a3c1afa798e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '31f3cf5aa9a46da5108905ff280a5fa7') in 0.177767 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Enviournment/Texture2D/concrete02_D.png
  artifactKey: Guid(5f012319526a17f4795d59615cc4b54f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Enviournment/Texture2D/concrete02_D.png using Guid(5f012319526a17f4795d59615cc4b54f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9e3262f9393e683a1fc7f84cd87fdc1e') in 0.115563 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Enviournment/Material/Container.mat
  artifactKey: Guid(91a12e40d38d6c44ca8bb28ee7eae401) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Enviournment/Material/Container.mat using Guid(91a12e40d38d6c44ca8bb28ee7eae401) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '69c646d7f24f68163980e702d2d2bf5f') in 0.045872 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Textures/Concrete slab_Normal.png
  artifactKey: Guid(3a0dde7201a17f646865e8deeac56596) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Textures/Concrete slab_Normal.png using Guid(3a0dde7201a17f646865e8deeac56596) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5b3d3357dd900dc6b5c05270f337e1e2') in 0.347226 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Enviournment/Avatar/ContainerAvatar.asset
  artifactKey: Guid(b522fb701adcf9a4582058b56dbf5101) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Enviournment/Avatar/ContainerAvatar.asset using Guid(b522fb701adcf9a4582058b56dbf5101) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6b34637460d2b1c4dbeb6d275b9f961a') in 0.082328 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Enviournment/Texture2D/Container.png
  artifactKey: Guid(2fd04d1116cd0b24ab813b104761e3d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Enviournment/Texture2D/Container.png using Guid(2fd04d1116cd0b24ab813b104761e3d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b738c6a5509a957aca93070b85b444b4') in 0.041499 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Newasset/Concrete3Diffuse.png
  artifactKey: Guid(f2796531f7e24514e8063f7b96ec5064) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Newasset/Concrete3Diffuse.png using Guid(f2796531f7e24514e8063f7b96ec5064) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1fadee187c453131f099c1a875c467e9') in 0.051978 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Enviournment/Texture2D/concrete_14_0.png
  artifactKey: Guid(f5b8a54f585d8e04abc8a800fa379c20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Enviournment/Texture2D/concrete_14_0.png using Guid(f5b8a54f585d8e04abc8a800fa379c20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b321b469018d3be763de4c4679efc950') in 0.022620 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Enviournment/Material/concrete02_D.mat
  artifactKey: Guid(a29580195092bac40a1d04a352facf3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Enviournment/Material/concrete02_D.mat using Guid(a29580195092bac40a1d04a352facf3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bb4896f5b4136706bd0475dc5d4aa055') in 0.266378 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 3.791249 seconds.
  path: Assets/plsyerasset/Luke Peek/Confetti/Materials/Confetti - Moon.mat
  artifactKey: Guid(764b44be9c7da4fa3be27280a64fadba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/plsyerasset/Luke Peek/Confetti/Materials/Confetti - Moon.mat using Guid(764b44be9c7da4fa3be27280a64fadba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1703cbcd30a3a2804b045ae2d8e881d4') in 0.107661 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/plsyerasset/Luke Peek/Confetti/Materials/Confetti - Star.mat
  artifactKey: Guid(225ea7271b39240e98f33593d02dbf05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/plsyerasset/Luke Peek/Confetti/Materials/Confetti - Star.mat using Guid(225ea7271b39240e98f33593d02dbf05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a8eb53a5b98b3eed30d4169937ce27c4') in 0.054471 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/plsyerasset/Luke Peek/Confetti/Textures/Confetti - Moon.png
  artifactKey: Guid(1c53400fbe73f4a02b8f54c3eb24ae24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/plsyerasset/Luke Peek/Confetti/Textures/Confetti - Moon.png using Guid(1c53400fbe73f4a02b8f54c3eb24ae24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9fa628d2b945f18533488877cd03c65b') in 0.035517 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/plsyerasset/Luke Peek/Confetti/Prefabs/Cannon with Ribbon/Confetti Cannon Ribbon - Moons & Stars.prefab
  artifactKey: Guid(b22b3de0151e44b1bbcb2b95626fc095) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/plsyerasset/Luke Peek/Confetti/Prefabs/Cannon with Ribbon/Confetti Cannon Ribbon - Moons & Stars.prefab using Guid(b22b3de0151e44b1bbcb2b95626fc095) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e4392e43b9d1739d2f52683e482bb5d6') in 0.072621 seconds 
Number of asset objects unloaded after import = 26
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Magic Spells and Popup effects/Particles/Confetti_02.prefab
  artifactKey: Guid(2ce06f0df2dea9545ac1b10625df2c11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Magic Spells and Popup effects/Particles/Confetti_02.prefab using Guid(2ce06f0df2dea9545ac1b10625df2c11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd2f18338e2b026f444ffc07d659327b8') in 0.026336 seconds 
Number of asset objects unloaded after import = 8
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0