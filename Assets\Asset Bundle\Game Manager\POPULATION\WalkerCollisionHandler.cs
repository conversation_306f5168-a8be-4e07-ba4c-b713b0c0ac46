using UnityEngine;

/// <summary>
/// Handles collision detection for individual walker prefabs
/// This component should be attached to each walker prefab
/// Simple approach: Death on OnTriggerEnter with player
/// Integrated with WaypointWalker system
/// </summary>
public class WalkerCollisionHandler : MonoBehaviour
{
    [<PERSON><PERSON>("Walker Components")]
    public Animator walkerAnimator;

    [<PERSON><PERSON>("Death Settings")]
    public string deathTrigger = "isDead";
    public float destroyDelay = 3f; // Time to wait before destroying walker after death

    private bool isDead = false;
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;

    void Start()
    {
        // Auto-find animator if not assigned
        if (walkerAnimator == null)
        {
            walkerAnimator = GetComponent<Animator>();
        }

        // Find the WaypointWalker in the scene and get this walker's index
        FindWaypointWalkerAndIndex();
    }

    /// <summary>
    /// Finds the WaypointWalker component and determines this walker's index
    /// </summary>
    void FindWaypointWalkerAndIndex()
    {
        // Find WaypointWalker in the scene
        waypointWalker = FindObjectOfType<WaypointWalker>();

        if (waypointWalker != null)
        {
            // Find this walker's index in the spawned prefabs list
            for (int i = 0; i < waypointWalker.spawnedPrefabs.Count; i++)
            {
                if (waypointWalker.spawnedPrefabs[i] == gameObject)
                {
                    walkerIndex = i;
                    Debug.Log($"🔗 Walker '{gameObject.name}' found at index {walkerIndex} in WaypointWalker system");
                    break;
                }
            }

            if (walkerIndex == -1)
            {
                Debug.LogWarning($"⚠️ Walker '{gameObject.name}' not found in WaypointWalker spawned prefabs list");
            }
        }
        else
        {
            Debug.LogWarning("⚠️ WaypointWalker not found in scene");
        }
    }

    /// <summary>
    /// Simple death trigger on any collision with player
    /// </summary>
    void OnTriggerEnter(Collider other)
    {
        // Check if already dead to prevent multiple triggers
        if (isDead) return;

        // Check if colliding object is the player
        if (other.CompareTag("Player"))
        {
            TriggerDeath();
        }
    }

    /// <summary>
    /// Triggers the death animation and marks walker as dead
    /// </summary>
    void TriggerDeath()
    {
        isDead = true;

        Debug.Log($"💀 Walker '{gameObject.name}' (index {walkerIndex}) death triggered by collision!");

        // If integrated with WaypointWalker, use its death system
        if (waypointWalker != null && walkerIndex >= 0)
        {
            waypointWalker.TriggerWalkerDeath(walkerIndex);
        }
        else
        {
            // Fallback: trigger death animation directly
            if (walkerAnimator != null)
            {
                walkerAnimator.SetTrigger(deathTrigger);
            }
        }

        // Disable collider to prevent further collisions
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.enabled = false;
        }
    }
}
