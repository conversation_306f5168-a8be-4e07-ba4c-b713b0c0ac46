using UnityEngine;

/// <summary>
/// Handles collision detection for individual walker prefabs
/// This component should be attached to each walker prefab
/// Simple approach: Death on OnTriggerEnter with player
/// </summary>
public class WalkerCollisionHandler : MonoBehaviour
{
    [Header("Walker Components")]
    public Animator walkerAnimator;

    [<PERSON><PERSON>("Death Settings")]
    public string deathTrigger = "Death";

    private bool isDead = false;

    void Start()
    {
        // Auto-find animator if not assigned
        if (walkerAnimator == null)
        {
            walkerAnimator = GetComponent<Animator>();
        }
    }

    /// <summary>
    /// Simple death trigger on any collision with player
    /// </summary>
    void OnTriggerEnter(Collider other)
    {
        // Check if already dead to prevent multiple triggers
        if (isDead) return;

        // Check if colliding object is the player
        if (other.CompareTag("Player"))
        {
            TriggerDeath();
        }
    }

    /// <summary>
    /// Triggers the death animation and marks walker as dead
    /// </summary>
    void TriggerDeath()
    {
        isDead = true;

        // Trigger death animation
        if (walkerAnimator != null)
        {
            walkerAnimator.SetTrigger(deathTrigger);
        }

        // Optional: Disable collider to prevent further collisions
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.enabled = false;
        }
    }
}
