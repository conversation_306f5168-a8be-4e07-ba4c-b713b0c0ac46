using UnityEngine;

/// <summary>
/// Handles collision detection for individual walker prefabs
/// This component should be attached to each walker prefab
/// Simple approach: Death on OnTriggerEnter with player
/// Integrated with WaypointWalker system
/// </summary>
public class WalkerCollisionHandler : MonoBehaviour
{
    [<PERSON><PERSON>("Walker Components")]
    public Animator walkerAnimator;

    [<PERSON><PERSON>("Death Settings")]
    public string deathTrigger = "isDead";

    [Head<PERSON>("Revival Settings")]
    public float revivalDistance = 30f; // Distance at which walker revives when player moves away

    private bool isDead = false;
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;

    void Start()
    {
        // Auto-find animator if not assigned
        if (walkerAnimator == null)
        {
            walkerAnimator = GetComponent<Animator>();
        }

        // Find the WaypointWalker in the scene and get this walker's index
        FindWaypointWalkerAndIndex();

        // Debug collider setup
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            Debug.Log($"🔧 Walker '{gameObject.name}' collider setup: isTrigger={col.isTrigger}, enabled={col.enabled}");
        }
        else
        {
            Debug.LogError($"❌ Walker '{gameObject.name}' has NO COLLIDER!");
        }
    }

    /// <summary>
    /// Finds the WaypointWalker component and determines this walker's index
    /// </summary>
    void FindWaypointWalkerAndIndex()
    {
        // Find WaypointWalker in the scene
        waypointWalker = FindObjectOfType<WaypointWalker>();

        if (waypointWalker != null)
        {
            // Find this walker's index in the spawned prefabs list
            for (int i = 0; i < waypointWalker.spawnedPrefabs.Count; i++)
            {
                if (waypointWalker.spawnedPrefabs[i] == gameObject)
                {
                    walkerIndex = i;
                    Debug.Log($"🔗 Walker '{gameObject.name}' found at index {walkerIndex} in WaypointWalker system");
                    break;
                }
            }

            if (walkerIndex == -1)
            {
                Debug.LogWarning($"⚠️ Walker '{gameObject.name}' not found in WaypointWalker spawned prefabs list");
            }
        }
        else
        {
            Debug.LogWarning("⚠️ WaypointWalker not found in scene");
        }
    }

    void Update()
    {
        // Check if walker should be revived when player moves far away
        if (isDead)
        {
            CheckForRevival();
        }
    }

    /// <summary>
    /// Checks if player is far enough to revive the walker
    /// Uses normal distance check (not directional)
    /// </summary>
    void CheckForRevival()
    {
        Vector3 playerPosition = GetPlayerPosition();
        if (playerPosition == Vector3.zero) return;

        // Simple distance check from walker center (not directional)
        float distanceToPlayer = Vector3.Distance(transform.position, playerPosition);

        if (distanceToPlayer >= revivalDistance)
        {
            ReviveWalker();
        }
    }

    /// <summary>
    /// Gets player position from RCC or fallback
    /// </summary>
    Vector3 GetPlayerPosition()
    {
        // Try to get player position from RCC Scene Manager first
        if (RCC_SceneManager.Instance != null && RCC_SceneManager.Instance.activePlayerVehicle != null)
        {
            return RCC_SceneManager.Instance.activePlayerVehicle.transform.position;
        }

        // Fallback: find player by tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            return player.transform.position;
        }

        return Vector3.zero;
    }

    /// <summary>
    /// Revives the walker when player moves far away
    /// </summary>
    void ReviveWalker()
    {
        isDead = false;

        Debug.Log($"🔄 Walker '{gameObject.name}' (index {walkerIndex}) revived - player moved {revivalDistance}m away!");

        // Fix position to ensure walker is on ground level
        FixWalkerPosition();

        // Re-enable collider
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.enabled = true;
        }

        // Reset walker state in WaypointWalker system
        if (waypointWalker != null && walkerIndex >= 0)
        {
            waypointWalker.ResetWalkerDeathState(walkerIndex);
        }

        // Reset animation to idle/walking state
        if (walkerAnimator != null)
        {
            // Reset death trigger and set to idle
            walkerAnimator.ResetTrigger(deathTrigger);
            walkerAnimator.SetBool("isWalking", false);
            walkerAnimator.SetBool("isIdle", true);
        }
    }

    /// <summary>
    /// Fixes walker position to ensure it's properly on ground level
    /// </summary>
    void FixWalkerPosition()
    {
        // Raycast downward to find ground
        RaycastHit hit;
        Vector3 rayStart = transform.position + Vector3.up * 2f; // Start ray 2 units above current position

        if (Physics.Raycast(rayStart, Vector3.down, out hit, 10f))
        {
            // Set walker position on ground
            Vector3 groundPosition = hit.point;
            transform.position = groundPosition;
            Debug.Log($"🔧 Walker '{gameObject.name}' position fixed to ground level: {groundPosition}");
        }
        else
        {
            // Fallback: just ensure Y position is at least 0
            Vector3 currentPos = transform.position;
            if (currentPos.y < 0)
            {
                transform.position = new Vector3(currentPos.x, 0f, currentPos.z);
                Debug.Log($"🔧 Walker '{gameObject.name}' Y position reset to 0");
            }
        }

        // Reset rotation to upright
        transform.rotation = Quaternion.Euler(0, transform.rotation.eulerAngles.y, 0);
    }

    /// <summary>
    /// Simple death trigger on any collision with player
    /// </summary>
    void OnTriggerEnter(Collider other)
    {
        Debug.Log($"🔍 Walker '{gameObject.name}' collision detected with: {other.name} (Tag: {other.tag})");

        // Check if already dead to prevent multiple triggers
        if (isDead)
        {
            Debug.Log($"⚠️ Walker '{gameObject.name}' is already dead, ignoring collision");
            return;
        }

        // Check if colliding object is the player
        if (other.CompareTag("Player"))
        {
            Debug.Log($"💀 Player collision confirmed! Triggering death for walker '{gameObject.name}'");
            TriggerDeath();
        }
        else
        {
            Debug.Log($"❌ Not a player collision. Object tag: {other.tag}");
        }
    }

    /// <summary>
    /// Triggers the death animation and marks walker as dead
    /// </summary>
    void TriggerDeath()
    {
        isDead = true;

        Debug.Log($"💀 Walker '{gameObject.name}' (index {walkerIndex}) death triggered by collision!");

        // If integrated with WaypointWalker, use its death system
        if (waypointWalker != null && walkerIndex >= 0)
        {
            waypointWalker.TriggerWalkerDeath(walkerIndex);
        }
        else
        {
            // Fallback: trigger death animation directly
            if (walkerAnimator != null)
            {
                walkerAnimator.SetTrigger(deathTrigger);
            }
        }

        // Disable collider to prevent further collisions
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.enabled = false;
        }
    }
}
