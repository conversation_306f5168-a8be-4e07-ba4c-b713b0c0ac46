Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker4.log
-srvPort
51145
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16700] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2189259596 [EditorId] 2189259596 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [16700] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2189259596 [EditorId] 2189259596 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 130.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56152
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.018004 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 416 ms
Refreshing native plugins compatible for Editor in 99.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.590 seconds
Domain Reload Profiling:
	ReloadAssembly (1591ms)
		BeginReloadAssembly (177ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (2ms)
		EndReloadAssembly (1227ms)
			LoadAssemblies (173ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (210ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (891ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (545ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (100ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (174ms)
				ProcessInitializeOnLoadMethodAttributes (69ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.017890 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.530 seconds
Domain Reload Profiling:
	ReloadAssembly (2532ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (2081ms)
			LoadAssemblies (185ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (390ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (109ms)
			SetupLoadedEditorAssemblies (1381ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (29ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1197ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 6.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2304 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (0.7 MB). Loaded Objects now: 2716.
Memory consumption went from 122.0 MB to 121.3 MB.
Total: 14.065600 ms (FindLiveObjects: 0.960100 ms CreateObjectMapping: 0.284800 ms MarkObjects: 10.866300 ms  DeleteObjects: 1.951900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3361.352428 seconds.
  path: Assets/GoogleMobileAds/Editor/Resources/PlaceholderAds/AdImages/728x90.png
  artifactKey: Guid(cfead63ff9d9d48e2b4ba3c2dee7cea9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GoogleMobileAds/Editor/Resources/PlaceholderAds/AdImages/728x90.png using Guid(cfead63ff9d9d48e2b4ba3c2dee7cea9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1e39c0c93b91382159375d3748309d1e') in 0.387886 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/tractor ui/changis/ad page  copy.png
  artifactKey: Guid(672738d72c5de734bb2af77252a5e653) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/ad page  copy.png using Guid(672738d72c5de734bb2af77252a5e653) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd140140700b98d922de9cc2dd78f637b') in 0.263444 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.632530 seconds.
  path: Assets/tractor ui/changis/ad page_.png
  artifactKey: Guid(ba2df42a6d5885545946a195e001dfb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/ad page_.png using Guid(ba2df42a6d5885545946a195e001dfb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1d02eb1abf6703a04c05fa550d33ebaf') in 0.341220 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 5.052151 seconds.
  path: Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Textures/Arrow_01.png
  artifactKey: Guid(04bfa53f4db1bab45aa28b8d3ea20bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Textures/Arrow_01.png using Guid(04bfa53f4db1bab45aa28b8d3ea20bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0f6938b2a4a902adf86c9ca887c557db') in 0.339312 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Textures/BG_Dropdown.png
  artifactKey: Guid(bb42e56a72d514a4da1c5e532bc02552) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Textures/BG_Dropdown.png using Guid(bb42e56a72d514a4da1c5e532bc02552) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b95cd8613258b80c55a97f59a2623367') in 0.437770 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Textures/BG_Template.png
  artifactKey: Guid(8fa9c03f7f55d67439305e3b652d776d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/EpicVictoryEffects (1)/EpicVictoryEffects/Textures/BG_Template.png using Guid(8fa9c03f7f55d67439305e3b652d776d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '83adf86e16476074d2a3b4db423a61d7') in 0.537231 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 22.873722 seconds.
  path: Assets/texture/blue.png
  artifactKey: Guid(2e9765590745e954bb30f555d3b87c9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/blue.png using Guid(2e9765590745e954bb30f555d3b87c9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cb699329a585302351b76c915d3038a3') in 0.389183 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Scenes/kachra/SLICING 2/SETTING/BUTTONS_ WHITE.png
  artifactKey: Guid(5874ccd85cfc3c04182ba13fc40724b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/SETTING/BUTTONS_ WHITE.png using Guid(5874ccd85cfc3c04182ba13fc40724b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7cdcc67a4703c97d3346646c01b8f341') in 0.191410 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/SETTING/BUTTONS_ WHITE.png
  artifactKey: Guid(a634351338e97954d9f533e943fc493c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/SETTING/BUTTONS_ WHITE.png using Guid(a634351338e97954d9f533e943fc493c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b4507ec75aca4fe7c84d9d282b817183') in 0.118638 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Dashboard/ButtonSprite.png
  artifactKey: Guid(ddbea74b2ba566442b107b58163ec442) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Dashboard/ButtonSprite.png using Guid(ddbea74b2ba566442b107b58163ec442) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f1a540d52e01508ecf2836db63ef9b8e') in 0.031122 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.350987 seconds.
  path: Assets/Scenes/kachra/SLICING 2/MAIN MENU/COINS.png
  artifactKey: Guid(9ea7b390e6d7c1c43b138f109e7d43c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/MAIN MENU/COINS.png using Guid(9ea7b390e6d7c1c43b138f109e7d43c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a1308f2fa97dd727dc818cf1d953b785') in 0.301706 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.143959 seconds.
  path: Assets/Scenes/kachra/SLICING 2/SETTING/CONTROL_.png
  artifactKey: Guid(9a61d33e1bbd7ae4ba015b3f096e684c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/SLICING 2/SETTING/CONTROL_.png using Guid(9a61d33e1bbd7ae4ba015b3f096e684c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f879fcfbc67f189b68c79ed181d2818d') in 0.111470 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Texture 1/decorative-pattern-5a3b9825cd89c5.2917847315138550138419-removebg-preview.png
  artifactKey: Guid(10ef7f5ac9d81a14b8bb7e57a7b1c441) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Texture 1/decorative-pattern-5a3b9825cd89c5.2917847315138550138419-removebg-preview.png using Guid(10ef7f5ac9d81a14b8bb7e57a7b1c441) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '39e543690fcc0fabbde0bf6dc3dd07a2') in 0.136144 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/tractor ui/changis/D.png
  artifactKey: Guid(74b33f0d2c95e954d9a5a3692e83bfb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/D.png using Guid(74b33f0d2c95e954d9a5a3692e83bfb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ea9c7be9799c670d695d9c71ba7e9787') in 0.066044 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.775612 seconds.
  path: Assets/texture/download__12_-removebg-preview.png
  artifactKey: Guid(06203893c461dff40981b141eb7f733e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/download__12_-removebg-preview.png using Guid(06203893c461dff40981b141eb7f733e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c9d6c41f7c25d180cdb5b7a60246d61a') in 0.097873 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.009370 seconds.
  path: Assets/texture/f869001f4b783e2c34d4d78c8d5e445c__1_-removebg-preview.png
  artifactKey: Guid(19045c98476bbb449ba09d880b103faa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/f869001f4b783e2c34d4d78c8d5e445c__1_-removebg-preview.png using Guid(19045c98476bbb449ba09d880b103faa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1af63a93084493fd37382d715f7a973e') in 0.314025 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 5.076614 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Mobile Textures/GradualGas.png
  artifactKey: Guid(76d083461419966478dc37dc2d29ea38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Mobile Textures/GradualGas.png using Guid(76d083461419966478dc37dc2d29ea38) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '754554f0ebcf2dcd74e947926f2ff4df') in 0.198247 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/texture/Group 17.png
  artifactKey: Guid(308911f4d88cab1499c60ee9c323cf7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/Group 17.png using Guid(308911f4d88cab1499c60ee9c323cf7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b3779a8689dc56e3d61666972bd3592') in 0.217855 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.239026 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/Group 19.png
  artifactKey: Guid(278399ef3aa764744a253161ca5169be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/LOADING SECREEN/Group 19.png using Guid(278399ef3aa764744a253161ca5169be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '37d418cff98cf0ba1195f49442b8eea7') in 0.126841 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/HEAT LIGHT OFF_.png
  artifactKey: Guid(153016896a6ed4349b6b78ce74b42ac4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/HEAT LIGHT OFF_.png using Guid(153016896a6ed4349b6b78ce74b42ac4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '81ec4a285729a24197cdb4799c7549d8') in 0.072646 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 3.106218 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/FAILED/HOME_.png
  artifactKey: Guid(05fb50a1f3b06924f9e2ffd40e028656) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/FAILED/HOME_.png using Guid(05fb50a1f3b06924f9e2ffd40e028656) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b071181997e2125b53e0a6aa9f0df2b') in 0.170596 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/textures 1/images__1_-removebg-preview.png
  artifactKey: Guid(8a150a239204e2d40ad4e6c30a374d92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/textures 1/images__1_-removebg-preview.png using Guid(8a150a239204e2d40ad4e6c30a374d92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '81f1a29c61756ca8cf51134a28526c65') in 0.180527 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 3.613253 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/INDICATOR WHITE.png
  artifactKey: Guid(bebe3ef34c88d884a81ba03f29de77c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/INDICATOR WHITE.png using Guid(bebe3ef34c88d884a81ba03f29de77c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '40fc1d22deda616f798c0bcb92fe1ef7') in 0.139119 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/COMPLETE/Layer 11.png
  artifactKey: Guid(54d0badccf94e19448d2f5f0ad84529a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/COMPLETE/Layer 11.png using Guid(54d0badccf94e19448d2f5f0ad84529a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9d5b0e2002bd535d76433c70cb282ad5') in 0.111136 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 4.926138 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 18.png
  artifactKey: Guid(a1f2cacd57cc08a449ce2b0b6d9389c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 18.png using Guid(a1f2cacd57cc08a449ce2b0b6d9389c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c07c749561b91d9075452da8d4e5d769') in 0.249350 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 694.png
  artifactKey: Guid(0cf36cd30f3dc094b8386499c490c515) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/MAIN/Layer 694.png using Guid(0cf36cd30f3dc094b8386499c490c515) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fbaea575f1720406d689e2003fc557cc') in 0.113180 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/PAUSE/Layer 680.png
  artifactKey: Guid(816eee14cfb90c04e92bd1e6158ce6f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/PAUSE/Layer 680.png using Guid(816eee14cfb90c04e92bd1e6158ce6f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '47a52a6cdee33f0f73773640ae595329') in 0.036071 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Layer 885 copy 2.png
  artifactKey: Guid(aaf1819e1d3f7bf468269c8b5d878045) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Layer 885 copy 2.png using Guid(aaf1819e1d3f7bf468269c8b5d878045) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ba09a759261e4bd07427f12974633e16') in 0.178535 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 8.175275 seconds.
  path: Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Layer 899.png
  artifactKey: Guid(da46868b2fb29864a8aa7e780dc0ee78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/SLICE (5)/SLICE/GAME PLAY/Layer 899.png using Guid(da46868b2fb29864a8aa7e780dc0ee78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4c23c542adb922fea6a3f63fff35a870') in 0.214819 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/tractor ui/farming levels/Layer 1294 (1).png
  artifactKey: Guid(b85298247a5d7854caa3025d24f3559a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/farming levels/Layer 1294 (1).png using Guid(b85298247a5d7854caa3025d24f3559a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7edd09d4bb01e8d36767d50f70282d62') in 0.180020 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/tractor ui/changis/LEVEL 03_.png
  artifactKey: Guid(e38406e3e05435140b6c5f9429523f42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/changis/LEVEL 03_.png using Guid(e38406e3e05435140b6c5f9429523f42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4efa2fa5f218b2c4272fea68e2d40bc9') in 0.285602 seconds 
Number of asset objects unloaded after import = 3
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0