Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker19
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker19.log
-srvPort
51145
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22988] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 557321196 [EditorId] 557321196 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [22988] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 557321196 [EditorId] 557321196 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 112.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56200
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.012418 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 551 ms
Refreshing native plugins compatible for Editor in 99.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.680 seconds
Domain Reload Profiling:
	ReloadAssembly (1680ms)
		BeginReloadAssembly (166ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1343ms)
			LoadAssemblies (163ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (205ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (1023ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (667ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (100ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (163ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.030516 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.287 seconds
Domain Reload Profiling:
	ReloadAssembly (2289ms)
		BeginReloadAssembly (217ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (1890ms)
			LoadAssemblies (156ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (329ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1281ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (1125ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 4.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2304 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (0.7 MB). Loaded Objects now: 2716.
Memory consumption went from 122.1 MB to 121.4 MB.
Total: 11.191100 ms (FindLiveObjects: 0.805800 ms CreateObjectMapping: 0.302000 ms MarkObjects: 8.786300 ms  DeleteObjects: 1.295100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 25914.230634 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainBlack.prefab
  artifactKey: Guid(00b626e3640c55c468dc862efe0b291b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainBlack.prefab using Guid(00b626e3640c55c468dc862efe0b291b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f53b36661f12140437469c4ec8b6cf26') in 0.246666 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 6.905895 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainBlue.prefab
  artifactKey: Guid(9a42469a1bf4eb948a482520fb7b031d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainBlue.prefab using Guid(9a42469a1bf4eb948a482520fb7b031d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '08e57c796203df2741b7aea73a7f8343') in 0.032863 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 1.965899 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainGreen.prefab
  artifactKey: Guid(91702ed4ff803484a9533b5cb2ef7dd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainGreen.prefab using Guid(91702ed4ff803484a9533b5cb2ef7dd0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ff6921884d9cc932a254cabd4e1cd517') in 0.024140 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 1.009638 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainMix.prefab
  artifactKey: Guid(08abdc5d49d191f4580bb4c00a82d6fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainMix.prefab using Guid(08abdc5d49d191f4580bb4c00a82d6fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9f39933d6eab2705c38099720bd5fd79') in 0.026012 seconds 
Number of asset objects unloaded after import = 18
========================================================================
Received Import Request.
  Time since last request: 4.751058 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainOrange.prefab
  artifactKey: Guid(56accb69ce134af4899884b062feab45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainOrange.prefab using Guid(56accb69ce134af4899884b062feab45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cff5d692ad47f0618e9f0d09fdbc4c2a') in 0.032058 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 1.009685 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainPurple.prefab
  artifactKey: Guid(0ca6a3ad568e21b4ca9c3dd7bc114fd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainPurple.prefab using Guid(0ca6a3ad568e21b4ca9c3dd7bc114fd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '25ce6d0910d0b73058b34e9ffb8c0899') in 0.027728 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 1.937866 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainYellow.prefab
  artifactKey: Guid(ffd79c729d40c664daad1472c2aac2c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainYellow.prefab using Guid(ffd79c729d40c664daad1472c2aac2c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dcfe174c7559be5009dc2341afe013ad') in 0.030058 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 3.378960 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionBlack.prefab
  artifactKey: Guid(022dd924128fa944cb68b078301800e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionBlack.prefab using Guid(022dd924128fa944cb68b078301800e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03888fc56a48a8e52a5b327fac119dd9') in 0.030442 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 0.896143 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionBlue.prefab
  artifactKey: Guid(a771f1d639b6be34ab98afe5e21afd63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionBlue.prefab using Guid(a771f1d639b6be34ab98afe5e21afd63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5e0258cd7a26fe9d0d1c28efa7de340a') in 0.031811 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 4.098077 seconds.
  path: Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalBlue.prefab
  artifactKey: Guid(b90d7a265f19c644c8bf6281c0174872) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalBlue.prefab using Guid(b90d7a265f19c644c8bf6281c0174872) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e3f76ed16514190186c437cf3c25ec58') in 0.030336 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 0.193993 seconds.
  path: Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalBlack.prefab
  artifactKey: Guid(d121e8d01e2803b419d07beba6c707cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalBlack.prefab using Guid(d121e8d01e2803b419d07beba6c707cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6e72bd3dfe1d9404f7fcca2386ce2f5') in 0.031468 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 3.215508 seconds.
  path: Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalGreen.prefab
  artifactKey: Guid(269c295108dad5d49a9743ba4b27efd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalGreen.prefab using Guid(269c295108dad5d49a9743ba4b27efd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '74f723daf03a26b39a885859d387a3cc') in 0.028817 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 1.310515 seconds.
  path: Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalPurple.prefab
  artifactKey: Guid(02509d55fd19507408d34dec06ddcf7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/DirectionalExplosion/ConfettiDirectionalPurple.prefab using Guid(02509d55fd19507408d34dec06ddcf7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7eb5ab92b1694623487dcc1f415aced') in 0.029477 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 14.780080 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainWhite.prefab
  artifactKey: Guid(547b410ad943af94b9fbaa9c4535b300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Fountain/ConfettiFountainWhite.prefab using Guid(547b410ad943af94b9fbaa9c4535b300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7c0ed5c9833a8e58e03969dccfac0a13') in 0.030227 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 1116.182420 seconds.
  path: Assets/RealisticCarControllerV3/Physics Materials/RCCAsphaltPhysics.physicMaterial
  artifactKey: Guid(509c1bbe5810ac84ab6aa68f6523684d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Physics Materials/RCCAsphaltPhysics.physicMaterial using Guid(509c1bbe5810ac84ab6aa68f6523684d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4cb86fb2682efe9f99fc9c5bdfa157c9') in 0.019963 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.006759 seconds.
  path: Assets/RealisticCarControllerV3/Physics Materials/RCCSandPhysics.physicMaterial
  artifactKey: Guid(1d81794eea45227458aa2965af4b201d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Physics Materials/RCCSandPhysics.physicMaterial using Guid(1d81794eea45227458aa2965af4b201d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2e894d29f55887246e720d952565fdb4') in 0.017107 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RealisticCarControllerV3/Physics Materials/RCCWallCollider.physicMaterial
  artifactKey: Guid(ef7b4e97c58befe4697c8a94f2931de0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Physics Materials/RCCWallCollider.physicMaterial using Guid(ef7b4e97c58befe4697c8a94f2931de0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8c0171305e55ace38d8523a14d07cdcf') in 0.017860 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/RealisticCarControllerV3/Physics Materials/RCCZeroFriction.physicMaterial
  artifactKey: Guid(dc74e74acd48f9d4d96a023f2bb05a4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Physics Materials/RCCZeroFriction.physicMaterial using Guid(dc74e74acd48f9d4d96a023f2bb05a4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6039bf8ee4be12047ccfff88f1ddd406') in 0.016894 seconds 
Number of asset objects unloaded after import = 1
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0